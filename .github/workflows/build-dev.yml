name: Build, Test, and Deploy SpringMath Dev to OVHCloud

on:
  push:
    branches:
      - feature/split_e2e
  pull_request:
    branches:
      - feature/split_e2e

env:
  NODE_VERSION: '22.16.0'
  METEOR_RELEASE: '3.3'

jobs:
  # Unit tests run first - fast feedback
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: app/package-lock.json

      - name: Cache Meteor installation
        uses: actions/cache@v4
        with:
          path: ~/.meteor
          key: meteor-${{ runner.os }}-${{ env.METEOR_RELEASE }}-${{ hashFiles('app/.meteor/versions') }}
          restore-keys: |
            meteor-${{ runner.os }}-${{ env.METEOR_RELEASE }}-
            meteor-${{ runner.os }}-

      - name: Cache system dependencies
        id: cache-system-deps
        uses: actions/cache@v4
        with:
          path: |
            ~/system-deps
          key: system-deps-${{ runner.os }}-libssl1.1

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libxss1

          # Check if libssl is already cached
          if [ ! -f ~/system-deps/libssl1.1_1.1.1f-1ubuntu2_amd64.deb ]; then
            mkdir -p ~/system-deps
            wget -q http://archive.ubuntu.com/ubuntu/pool/main/o/openssl/libssl1.1_1.1.1f-1ubuntu2_amd64.deb -O ~/system-deps/libssl1.1_1.1.1f-1ubuntu2_amd64.deb
          fi
          sudo dpkg -i ~/system-deps/libssl1.1_1.1.1f-1ubuntu2_amd64.deb

      - name: Cache MongoDB binaries
        uses: actions/cache@v4
        with:
          path: ~/.mongodb-binaries
          key: mongodb-binaries-${{ runner.os }}-${{ hashFiles('app/imports/scripts/exportScores/.scripts/initMongoDb.js') }}
          restore-keys: |
            mongodb-binaries-${{ runner.os }}-

      - name: Cache schoolScrubber dependencies
        uses: actions/cache@v4
        with:
          path: app/.scripts/schoolScrubber/node_modules
          key: school-scrubber-${{ runner.os }}-${{ hashFiles('app/.scripts/schoolScrubber/package-lock.json') }}
          restore-keys: |
            school-scrubber-${{ runner.os }}-

      - name: Install Node dependencies
        working-directory: app
        run: |
          npm ci
          if [ ! -d .scripts/schoolScrubber/node_modules ]; then
            cd .scripts/schoolScrubber && npm ci
          fi

      - name: Install Meteor
        run: |
          if [ ! -e $HOME/.meteor/meteor ]; then 
            curl -sS https://install.meteor.com/?release=${{ env.METEOR_RELEASE }} | /bin/sh
          fi
          sudo ln -sf ~/.meteor/meteor /usr/local/bin/meteor

      - name: Initialize MongoDB for tests
        working-directory: app/imports/scripts/exportScores/.scripts
        run: |
          if [ ! -e ~/.mongodb-binaries ]; then 
            node initMongoDb.js
          fi

      - name: Run unit tests
        working-directory: app
        run: |
          BABEL_ENV=unittesting npx jest --maxWorkers=2 --ci \
            --reporters=default --reporters=jest-junit
        env:
          JEST_JUNIT_OUTPUT_DIR: ./test-results
          JEST_JUNIT_OUTPUT_NAME: junit.xml

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: unit-test-results
          path: app/test-results/

  # Parallel E2E tests
  e2e-tests:
    # Skip E2E tests if DEV_SKIP_E2E_TESTS is set to 'true'
    if: vars.DEV_SKIP_E2E_TESTS != 'true'
    # Using ubuntu-latest (2 vCPUs, 7 GB RAM)
    # Alternative: ubuntu-latest-4-cores (4 vCPUs, 16 GB RAM) for better performance
    runs-on: ubuntu-latest
    permissions:
      contents: read
      actions: write # Required for downloading artifacts from other workflow runs
      checks: write
      pull-requests: read # Required for accessing PR-related artifacts
      repository-projects: read # Additional permission for artifact access
    needs: unit-tests
    strategy:
      fail-fast: false
      matrix:
        shard: [1, 2, 3, 4]

    # No MongoDB service container - using Meteor's built-in MongoDB instead

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: app/package-lock.json

      - name: Cache Cypress binary
        uses: actions/cache@v4
        with:
          path: ~/.cache/Cypress
          key: cypress-${{ runner.os }}-${{ hashFiles('app/package-lock.json') }}

      - name: Cache Meteor
        uses: actions/cache@v4
        with:
          path: |
            ~/.meteor
            app/.meteor/local
          key: meteor-e2e-${{ runner.os }}-${{ env.METEOR_RELEASE }}-${{ hashFiles('app/.meteor/versions') }}
          restore-keys: |
            meteor-e2e-${{ runner.os }}-${{ env.METEOR_RELEASE }}-

      - name: Cache system dependencies
        id: cache-system-deps
        uses: actions/cache@v4
        with:
          path: |
            ~/system-deps
          key: system-deps-${{ runner.os }}-libxss1

      - name: Install system dependencies
        run: |
          # Only update apt if we need to install something
          if ! command -v xvfb &> /dev/null || [ ! -f ~/system-deps/installed-marker ]; then
            sudo apt-get update && sudo apt-get install -y libxss1
            mkdir -p ~/system-deps
            touch ~/system-deps/installed-marker
          fi

          # Install MongoDB tools for mongorestore
          echo "=== Installing MongoDB Database Tools ==="
          if ! command -v mongorestore &> /dev/null; then
            wget -q https://fastdl.mongodb.org/tools/db/mongodb-database-tools-ubuntu2204-x86_64-100.10.0.deb -O /tmp/mongodb-tools.deb
            if [ ! -f /tmp/mongodb-tools.deb ]; then
              echo "ERROR: Failed to download MongoDB tools"
              exit 1
            fi
            
            sudo dpkg -i /tmp/mongodb-tools.deb || {
              echo "dpkg install failed, trying to fix dependencies..."
              sudo apt-get update && sudo apt-get install -f -y
              sudo dpkg -i /tmp/mongodb-tools.deb
            }
            
            # Verify mongorestore is installed
            which mongorestore && echo "✓ mongorestore installed at: $(which mongorestore)" || {
              echo "ERROR: mongorestore not found after installation"
              echo "Checking installed files:"
              dpkg -L mongodb-database-tools | grep mongorestore || echo "mongorestore not in package"
              exit 1
            }
          else
            echo "✓ mongorestore already installed at: $(which mongorestore)"
          fi

      - name: Cache schoolScrubber dependencies
        uses: actions/cache@v4
        with:
          path: app/.scripts/schoolScrubber/node_modules
          key: school-scrubber-${{ runner.os }}-${{ hashFiles('app/.scripts/schoolScrubber/package-lock.json') }}
          restore-keys: |
            school-scrubber-${{ runner.os }}-

      - name: Install dependencies
        working-directory: app
        run: |
          npm ci
          if [ ! -e $HOME/.meteor/meteor ]; then 
            curl -sS https://install.meteor.com/?release=${{ env.METEOR_RELEASE }} | /bin/sh
          fi
          sudo ln -sf ~/.meteor/meteor /usr/local/bin/meteor

      - name: Prepare test environment
        working-directory: app
        run: |
          # Copy test settings
          cp .scripts/settings-for-testing.json settings.json

      - name: Download and prepare historical E2E timing data
        id: download-e2e-reports
        run: |
          echo "Downloading historical E2E test timing data from previous runs on branch: ${{ github.ref_name }}"

          # Create directories for historical and current timing data
          mkdir -p app/junit-xml-reports-downloaded
          mkdir -p app/junit-xml-reports-historical
          mkdir -p app/junit-xml-reports

          # Try to download each shard's junit reports from previous runs
          FOUND_ANY=false

          for shard in 1 2 3 4; do
            echo "Downloading historical timing data for shard ${shard}..."
            if curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
                    -H "Accept: application/vnd.github.v3+json" \
                    "https://api.github.com/repos/${{ github.repository }}/actions/artifacts" \
                    | jq -r --arg name "junit-xml-reports-shard-${shard}" \
                           --arg branch "${{ github.ref_name }}" \
                           '.artifacts[] | select(.name == $name and .workflow_run.head_branch == $branch) | .archive_download_url' \
                    | head -1 | grep -q "https://"; then

              DOWNLOAD_URL=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
                                  -H "Accept: application/vnd.github.v3+json" \
                                  "https://api.github.com/repos/${{ github.repository }}/actions/artifacts" \
                                  | jq -r --arg name "junit-xml-reports-shard-${shard}" \
                                         --arg branch "${{ github.ref_name }}" \
                                         '.artifacts[] | select(.name == $name and .workflow_run.head_branch == $branch) | .archive_download_url' \
                                  | head -1)

              if [ -n "$DOWNLOAD_URL" ] && [ "$DOWNLOAD_URL" != "null" ]; then
                echo "✓ Found historical timing data for shard ${shard}, downloading..."
                curl -L -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
                     "$DOWNLOAD_URL" -o "/tmp/shard-${shard}.zip"

                if [ -f "/tmp/shard-${shard}.zip" ]; then
                  # Extract to historical directory first
                  cd app/junit-xml-reports-historical
                  unzip -q "/tmp/shard-${shard}.zip" || true
                  cd ../..
                  FOUND_ANY=true

                  # Copy historical data to the main reports directory for split-tests-by-timings
                  if [ -d "app/junit-xml-reports-historical" ]; then
                    cp -r app/junit-xml-reports-historical/* app/junit-xml-reports-downloaded/ 2>/dev/null || true
                  fi
                fi
              fi
            else
              echo "✗ No historical timing data found for shard ${shard}"
            fi
          done

          echo "found_e2e=$FOUND_ANY" >> $GITHUB_OUTPUT

      - name: Verify historical E2E timing data availability
        run: |
          echo "=== E2E Timing Data Status ==="

          if [ -d "app/junit-xml-reports-downloaded" ] && [ "$(find app/junit-xml-reports-downloaded -name '*.xml' | wc -l)" -gt 0 ]; then
            echo "✓ Successfully downloaded historical E2E test timing data"
            echo "  XML files found: $(find app/junit-xml-reports-downloaded -name '*.xml' | wc -l)"
            echo "  Source: Accumulated E2E test reports from ${{ github.ref_name }} branch"
            echo ""

            # Show timing data coverage
            echo "Timing data coverage analysis:"
            find app/junit-xml-reports-downloaded -name '*.xml' -exec echo "  - {}" \; -exec xmlstarlet sel -t -v 'count(//testcase)' {} \; 2>/dev/null | paste - - | sed 's/\t/ test cases)/' | sed 's/^/  (' || true
            echo ""
            echo "✓ split-tests-by-timings will use this accumulated timing data for optimal test distribution"
            echo "  This includes timing data from all previous successful runs on this branch"

          else
            echo "ℹ No historical E2E test timing data available from ${{ github.ref_name }} branch"
            echo "  This is normal for:"
            echo "  - First run on this branch"
            echo "  - Branches without previous successful E2E test runs"
            echo "  - When previous runs had no E2E tests executed"
            echo ""
            echo "ℹ split-tests-by-timings will fall back to file count-based distribution"
            echo "  After this run completes successfully, timing data will be available for future runs"
          fi

      - name: Compute node_index
        id: compute_index
        run: |
          echo "node_index=$(( ${{ matrix.shard }} - 1 ))" >> $GITHUB_OUTPUT

      - name: Debug - Check artifact download permissions and status
        run: |
          echo "=== Checking artifact download permissions and status ==="
          echo "GitHub token permissions:"
          echo "- GITHUB_TOKEN is available: $([ -n '${{ secrets.GITHUB_TOKEN }}' ] && echo 'YES' || echo 'NO')"
          echo "- Repository: ${{ github.repository }}"
          echo "- Branch: ${{ github.ref_name }}"
          echo "- Workflow: build-dev.yml"
          echo ""

          # Check if we can access the GitHub API
          echo "Testing GitHub API access:"
          curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
               -H "Accept: application/vnd.github.v3+json" \
               "https://api.github.com/repos/${{ github.repository }}/actions/artifacts?per_page=5" \
               | jq -r '.artifacts[] | "\(.name) - \(.created_at) - \(.workflow_run.head_branch)"' 2>/dev/null || \
               echo "Failed to access GitHub API or no artifacts found"
          echo ""

      - name: Install debugging tools
        run: |
          # Install xmllint for XML validation and jq for JSON parsing
          sudo apt-get update -qq
          sudo apt-get install -y libxml2-utils jq

      - name: Debug - Verify downloaded artifacts
        working-directory: app
        run: |
          echo "=== Debugging split-tests-by-timings timing data issue ==="
          echo "Current working directory: $(pwd)"
          echo "Repository structure:"
          ls -la ../
          echo ""
          echo "App directory structure:"
          ls -la .
          echo ""

          # Check if junit-xml-reports-downloaded exists
          if [ -d "junit-xml-reports-downloaded" ]; then
            echo "✓ junit-xml-reports-downloaded directory exists"
            echo "Contents of junit-xml-reports-downloaded:"
            ls -la junit-xml-reports-downloaded/
            echo ""

            # Check for XML files
            XML_FILES=$(find junit-xml-reports-downloaded -name "*.xml" 2>/dev/null || true)
            if [ -n "$XML_FILES" ]; then
              echo "✓ Found XML files:"
              echo "$XML_FILES" | while read -r file; do
                if [ -f "$file" ]; then
                  echo "  - $file ($(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo "unknown") bytes, modified: $(stat -f%Sm "$file" 2>/dev/null || stat -c%y "$file" 2>/dev/null || echo "unknown"))"
                fi
              done
            else
              echo "✗ No XML files found in junit-xml-reports-downloaded"
            fi
          else
            echo "✗ junit-xml-reports-downloaded directory does not exist"
            echo "Available directories:"
            find . -type d -name "*junit*" -o -name "*report*" 2>/dev/null || echo "No junit or report directories found"
          fi

      - name: Debug - Validate JUnit XML format and timing data
        working-directory: app
        run: |
          echo "=== Analyzing JUnit XML files for timing data ==="

          if [ -d "junit-xml-reports-downloaded" ]; then
            find junit-xml-reports-downloaded -name "*.xml" | head -3 | while read -r xml_file; do
              if [ -f "$xml_file" ]; then
                echo ""
                echo "--- Analyzing: $xml_file ---"
                echo "File size: $(stat -f%z "$xml_file" 2>/dev/null || stat -c%s "$xml_file" 2>/dev/null || echo "unknown") bytes"

                # Check if file is valid XML
                if command -v xmllint >/dev/null 2>&1; then
                  if xmllint --noout "$xml_file" 2>/dev/null; then
                    echo "✓ Valid XML format"
                  else
                    echo "✗ Invalid XML format"
                    echo "First 10 lines of file:"
                    head -10 "$xml_file"
                    continue
                  fi
                else
                  echo "⚠ xmllint not available, skipping XML validation"
                fi

                # Show XML structure and look for timing attributes
                echo "XML structure (first 20 lines):"
                head -20 "$xml_file"
                echo ""

                # Look for testcase elements with time attributes
                echo "Testcase elements with timing data:"
                grep -n "testcase.*time=" "$xml_file" | head -5 || echo "No testcase elements with time attributes found"
                echo ""

                # Look for file paths in the XML
                echo "File paths found in XML:"
                grep -o 'file="[^"]*"' "$xml_file" | head -5 || echo "No file attributes found"
                grep -o 'classname="[^"]*"' "$xml_file" | head -5 || echo "No classname attributes found"
                grep -o 'name="[^"]*"' "$xml_file" | head -5 || echo "No name attributes found"
                echo ""

                # Check for any cypress/integration paths
                echo "Cypress integration paths:"
                grep -i "cypress.*integration" "$xml_file" | head -3 || echo "No cypress integration paths found"
                echo ""
              fi
            done
          else
            echo "No junit-xml-reports-downloaded directory found"
          fi

      - name: Debug - Test glob pattern and file matching
        working-directory: app
        run: |
          echo "=== Testing glob pattern and file matching ==="
          echo "Current directory: $(pwd)"
          echo ""

          # Test the glob pattern
          echo "Testing glob pattern: tests/cypress/integration/**/*.spec.js"
          GLOB_FILES=$(find tests/cypress/integration -name "*.spec.js" 2>/dev/null | sort || true)
          if [ -n "$GLOB_FILES" ]; then
            echo "✓ Glob pattern found $(echo "$GLOB_FILES" | wc -l) files:"
            echo "$GLOB_FILES" | head -10
            if [ $(echo "$GLOB_FILES" | wc -l) -gt 10 ]; then
              echo "... and $(( $(echo "$GLOB_FILES" | wc -l) - 10 )) more files"
            fi
          else
            echo "✗ Glob pattern found no files"
            echo "Available test directories:"
            find . -type d -name "*test*" -o -name "*spec*" -o -name "*cypress*" 2>/dev/null || echo "No test directories found"
          fi
          echo ""

          # Check specific file mentioned in warning
          TARGET_FILE="tests/cypress/integration/addCoach.spec.js"
          if [ -f "$TARGET_FILE" ]; then
            echo "✓ Target file exists: $TARGET_FILE"
            echo "  Size: $(stat -f%z "$TARGET_FILE" 2>/dev/null || stat -c%s "$TARGET_FILE" 2>/dev/null || echo "unknown") bytes"
            echo "  Modified: $(stat -f%Sm "$TARGET_FILE" 2>/dev/null || stat -c%y "$TARGET_FILE" 2>/dev/null || echo "unknown")"
          else
            echo "✗ Target file does not exist: $TARGET_FILE"
          fi
          echo ""

          # Show absolute paths that would appear in JUnit reports
          echo "Absolute paths (as they would appear in JUnit reports):"
          echo "Current absolute path: $(pwd)"
          if [ -f "$TARGET_FILE" ]; then
            echo "Target file absolute path: $(cd "$(dirname "$TARGET_FILE")" && pwd)/$(basename "$TARGET_FILE")"
          fi

      - name: Debug - Compare XML paths with actual files
        working-directory: app
        run: |
          echo "=== Comparing XML report paths with actual test files ==="

          if [ -d "junit-xml-reports-downloaded" ]; then
            # Extract all file paths from XML reports
            echo "Extracting file paths from XML reports..."
            XML_PATHS_FILE="/tmp/xml_paths.txt"
            > "$XML_PATHS_FILE"

            find junit-xml-reports-downloaded -name "*.xml" | while read -r xml_file; do
              # Look for various path patterns in XML
              grep -o 'file="[^"]*"' "$xml_file" 2>/dev/null | sed 's/file="//;s/"//' >> "$XML_PATHS_FILE" || true
              grep -o 'classname="[^"]*"' "$xml_file" 2>/dev/null | sed 's/classname="//;s/"//' >> "$XML_PATHS_FILE" || true
              # Look for paths in test names or other attributes
              grep -o '/[^"]*\.spec\.js' "$xml_file" 2>/dev/null >> "$XML_PATHS_FILE" || true
            done

            if [ -s "$XML_PATHS_FILE" ]; then
              echo "Unique paths found in XML reports:"
              sort "$XML_PATHS_FILE" | uniq | head -10
              echo ""

              # Check if any paths match our target pattern
              echo "Paths containing 'addCoach.spec.js':"
              grep "addCoach.spec.js" "$XML_PATHS_FILE" || echo "No addCoach.spec.js paths found"
              echo ""

              echo "Paths containing 'cypress/integration':"
              grep "cypress/integration" "$XML_PATHS_FILE" | head -5 || echo "No cypress/integration paths found"
              echo ""
            else
              echo "No file paths extracted from XML reports"
            fi

            rm -f "$XML_PATHS_FILE"
          else
            echo "No XML reports directory found"
          fi

          # List actual test files for comparison
          echo "Actual test files found:"
          find tests/cypress/integration -name "*.spec.js" 2>/dev/null | head -10 || echo "No test files found"

      - name: Split tests by timings
        id: split-tests
        uses: r7kamura/split-tests-by-timings@v0
        with:
          reports: junit-xml-reports-downloaded
          glob: tests/cypress/integration/**/*.spec.js
          index: ${{ steps.compute_index.outputs.node_index }}
          total: 4
          working-directory: app
        continue-on-error: true

      - name: Debug - Analyze split-tests-by-timings output
        working-directory: app
        run: |
          echo "=== Analyzing split-tests-by-timings action output ==="
          echo "Action step outcome: ${{ steps.split-tests.outcome }}"
          echo "Action step conclusion: ${{ steps.split-tests.conclusion }}"
          echo ""

          if [ -n "${{ steps.split-tests.outputs.paths }}" ]; then
            echo "✓ split-tests-by-timings produced output:"
            echo "Raw output: '${{ steps.split-tests.outputs.paths }}'"
            echo ""
            echo "Parsed paths:"
            echo "${{ steps.split-tests.outputs.paths }}" | tr ' ' '\n' | nl
            echo ""
            echo "Number of test files assigned to this shard: $(echo '${{ steps.split-tests.outputs.paths }}' | wc -w)"
          else
            echo "✗ split-tests-by-timings produced no output"
            echo "This means the action either failed or couldn't find timing data"
          fi
          echo ""

          # Check if the action created any temporary files or logs
          echo "Checking for any temporary files or logs created by the action:"
          find . -name "*split*" -o -name "*timing*" -o -name "*junit*" -type f -newer junit-xml-reports-downloaded 2>/dev/null | head -10 || echo "No recent files found"
          echo ""

          # Verify the action inputs were correct
          echo "Verifying action inputs:"
          echo "- reports directory exists: $([ -d 'junit-xml-reports-downloaded' ] && echo 'YES' || echo 'NO')"
          echo "- glob pattern matches files: $(find tests/cypress/integration -name '*.spec.js' 2>/dev/null | wc -l) files"
          echo "- index: ${{ steps.compute_index.outputs.node_index }}"
          echo "- total: 4"
          echo "- working-directory: app (current: $(pwd))"

      - name: Debug - Detailed JUnit XML analysis for split-tests compatibility
        working-directory: app
        run: |
          echo "=== Detailed JUnit XML analysis for split-tests-by-timings compatibility ==="

          if [ -d "junit-xml-reports-downloaded" ]; then
            # Find the most recent XML file
            LATEST_XML=$(find junit-xml-reports-downloaded -name "*.xml" -type f -exec ls -t {} + | head -1)

            if [ -n "$LATEST_XML" ] && [ -f "$LATEST_XML" ]; then
              echo "Analyzing latest XML file: $LATEST_XML"
              echo ""

              # Show the complete structure of one XML file
              echo "=== Complete XML structure ==="
              cat "$LATEST_XML"
              echo ""
              echo "=== End of XML structure ==="
              echo ""

              # Extract specific elements that split-tests-by-timings looks for
              echo "=== Elements that split-tests-by-timings action looks for ==="
              echo ""

              echo "1. Testsuite elements:"
              xmllint --xpath "//testsuite" "$LATEST_XML" 2>/dev/null || echo "No testsuite elements found"
              echo ""

              echo "2. Testcase elements with time attributes:"
              xmllint --xpath "//testcase[@time]" "$LATEST_XML" 2>/dev/null || echo "No testcase elements with time attributes found"
              echo ""

              echo "3. All testcase elements (regardless of time attribute):"
              xmllint --xpath "//testcase" "$LATEST_XML" 2>/dev/null || echo "No testcase elements found"
              echo ""

              echo "4. File/classname attributes in testcase elements:"
              xmllint --xpath "//testcase/@file" "$LATEST_XML" 2>/dev/null || echo "No file attributes found"
              xmllint --xpath "//testcase/@classname" "$LATEST_XML" 2>/dev/null || echo "No classname attributes found"
              echo ""

              # Check what the action specifically needs
              echo "=== What split-tests-by-timings needs ==="
              echo "The action needs:"
              echo "1. <testcase> elements with 'time' attributes for timing data"
              echo "2. File path information in 'file' or 'classname' attributes"
              echo "3. Paths that match the glob pattern when resolved"
              echo ""

              # Show raw timing data extraction
              echo "Raw timing data extraction:"
              grep -o '<testcase[^>]*time="[^"]*"[^>]*>' "$LATEST_XML" | head -5 || echo "No testcase elements with time attributes found in raw grep"

            else
              echo "No XML files found to analyze"
            fi
          else
            echo "No junit-xml-reports-downloaded directory found"
          fi

      - name: Get test specs for this shard
        id: specs
        working-directory: app
        run: |
          # Check if split-tests-by-timings produced output
          if [ -n "${{ steps.split-tests.outputs.paths }}" ]; then
            # Use the output from split-tests-by-timings (paths are already relative to app directory)
            SPECS=$(echo "${{ steps.split-tests.outputs.paths }}" | tr ' ' ',' || true)
            echo "Using timing-based test distribution"
          else
            # Fallback to the original method if split-tests-by-timings failed
            echo "Falling back to file count-based distribution"
            # Get all spec files
            ALL_SPECS=$(find tests/cypress/integration -name "*.spec.js" 2>/dev/null | sort || true)

            if [ -z "$ALL_SPECS" ]; then
              echo "No test specs found, skipping E2E tests"
              echo "specs=" >> $GITHUB_OUTPUT
              exit 0
            fi

            TOTAL=$(echo "$ALL_SPECS" | wc -l)

            # Calculate shard boundaries
            SHARD_SIZE=$(( ($TOTAL + 3) / 4 ))
            START=$(( (${{ matrix.shard }} - 1) * $SHARD_SIZE + 1 ))
            END=$(( ${{ matrix.shard }} * $SHARD_SIZE ))

            # Get specs for this shard
            SPECS=$(echo "$ALL_SPECS" | sed -n "${START},${END}p" | tr '\n' ',' || true)
          fi

          echo "specs=${SPECS%,}" >> $GITHUB_OUTPUT

          # Display for logs
          echo "Shard ${{ matrix.shard }} running specs:"
          echo "${SPECS%,}" | tr ',' '\n' | xargs -n1 basename || true

      - name: Debug system resources
        if: steps.specs.outputs.specs != ''
        run: |
          echo "=== System Resources ==="
          echo "CPU cores: $(nproc)"
          echo "Memory:"
          free -h
          echo ""
          echo "Disk space:"
          df -h /
          echo ""
          echo "=== Process list (top 10 by memory) ==="
          ps aux --sort=-%mem | head -11

      - name: Login to OVH Cloud Registry
        if: steps.specs.outputs.specs != ''
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.OVHCLOUD_REGISTRY_URL }}
          username: ${{ vars.OVHCLOUD_REGISTRY_USERNAME }}
          password: ${{ secrets.OVHCLOUD_REGISTRY_ACCESS_TOKEN }}

      - name: Extract test data from MongoDB Docker image
        if: steps.specs.outputs.specs != ''
        run: |
          echo "=== Extracting test data from MongoDB image ==="
          # Pull the image first
          docker pull ${{ vars.OVHCLOUD_REGISTRY_URL }}/${{ vars.OVHCLOUD_REGISTRY_PROJECT_NAME }}/spring-math-test-ci:8.0

          # Create a temporary container to extract the test data
          docker create --name temp-mongo ${{ vars.OVHCLOUD_REGISTRY_URL }}/${{ vars.OVHCLOUD_REGISTRY_PROJECT_NAME }}/spring-math-test-ci:8.0

          # Extract the meteor.tar file that contains the test data
          docker cp temp-mongo:/meteor.tar /tmp/meteor.tar

          # Extract the dump files
          cd /tmp
          tar -xzf meteor.tar

          # Clean up temporary container
          docker rm temp-mongo

          echo "Test data extracted to /tmp/dump/meteor/"
          ls -la /tmp/dump/meteor/ | head -10

      - name: Start Meteor application with built-in MongoDB
        if: steps.specs.outputs.specs != ''
        working-directory: app
        run: |
          echo "=== Starting Meteor at $(date '+%Y-%m-%d %H:%M:%S') ==="
          echo "Using Meteor's built-in MongoDB (no external MongoDB needed)"

          # Create a log file for Meteor output
          touch meteor-startup.log

          # Start Meteor WITHOUT external MongoDB URL (uses built-in MongoDB on port 3001)
          echo "Starting Meteor with built-in MongoDB..."
          export ROOT_URL="http://localhost:3000"
          export METEOR_ENVIRONMENT=TEST
          export SERVER_WEBSOCKET_COMPRESSION=0
          export CI=true

          # Start Meteor with output to log file
          meteor --settings settings.json --port 3000 2>&1 | tee -a meteor-startup.log &

          METEOR_PID=$!
          echo "Meteor PID: $METEOR_PID"

          # Wait for MongoDB to start (watch for "=> Started MongoDB." in logs)
          echo "Waiting for MongoDB to start..."
          MONGO_WAIT=0
          while ! grep -q "=> Started MongoDB\." meteor-startup.log; do
            sleep 1
            MONGO_WAIT=$((MONGO_WAIT + 1))
            if [ $MONGO_WAIT -ge 90 ]; then
              echo "MongoDB not started after 90 seconds"
              tail -20 meteor-startup.log
              exit 1
            fi
          done
          echo "MongoDB started after ${MONGO_WAIT} seconds"

          # Give MongoDB a moment to fully initialize
          sleep 2

          # Restore test data immediately after MongoDB starts
          echo "=== Restoring test data to Meteor's MongoDB ==="

          # Check if mongorestore is available
          if ! command -v mongorestore &> /dev/null; then
            echo "ERROR: mongorestore command not found"
            echo "PATH=$PATH"
            echo "Checking for mongorestore in common locations:"
            ls -la /usr/bin/mongorestore 2>/dev/null || echo "/usr/bin/mongorestore not found"
            ls -la /usr/local/bin/mongorestore 2>/dev/null || echo "/usr/local/bin/mongorestore not found"
            exit 1
          fi

          cd /tmp/dump
          mongorestore --port 3001 --db meteor meteor/ --noIndexRestore
          echo "✓ Test data restored successfully!"

          # Now wait for Meteor app to be ready
          cd ~/work/springmath-app/springmath-app/app
          echo "Waiting for Meteor application to be ready..."
          START_TIME=$(date +%s)
          TIMEOUT=120

          while true; do
            CURRENT_TIME=$(date +%s)
            ELAPSED=$((CURRENT_TIME - START_TIME))
            
            # Check if Meteor is responding
            if curl -s http://localhost:3000 > /dev/null 2>&1; then
              echo "✓ Meteor application is ready after ${ELAPSED} seconds!"
              break
            fi
            
            # Check for timeout
            if [ $ELAPSED -ge $TIMEOUT ]; then
              echo "✗ Timeout: Meteor failed to start within ${TIMEOUT} seconds"
              echo "=== Last 50 lines of Meteor log ==="
              tail -50 meteor-startup.log
              exit 1
            fi
            
            # Show progress every 10 seconds
            if [ $((ELAPSED % 10)) -eq 0 ]; then
              echo "  Still waiting... (${ELAPSED}s elapsed)"
            fi
            
            sleep 1
          done

      - name: Run Cypress tests with resource monitoring
        if: steps.specs.outputs.specs != ''
        working-directory: app
        run: |
          echo "=== Starting Cypress tests at $(date '+%Y-%m-%d %H:%M:%S') ==="
          echo "Memory before tests:"
          free -h

          # Run Cypress with resource monitoring
          npx cypress run \
            --config-file cypress.gh.config.js \
            --spec "${{ steps.specs.outputs.specs }}" \
            --reporter junit \
            --reporter-options "mochaFile=junit-xml-reports/junit-xml-report-${{ matrix.shard }}.xml" &

          CYPRESS_PID=$!

          # Monitor resources during test execution
          MONITOR_INTERVAL=30
          while kill -0 $CYPRESS_PID 2>/dev/null; do
            sleep $MONITOR_INTERVAL
            echo "=== Resource check at $(date '+%H:%M:%S') ==="
            echo "Memory: $(free -h | grep "^Mem:" | awk '{print "Used: " $3 " / " $2}')"
            echo "Load average: $(uptime | awk -F'load average:' '{print $2}')"
            echo "Top 5 processes by CPU:"
            ps aux --sort=-%cpu | head -6 | tail -5 | awk '{print $2, $3, $4, $11}'
          done

          # Wait for Cypress to complete
          wait $CYPRESS_PID
          CYPRESS_EXIT_CODE=$?

          echo "=== Cypress tests completed with exit code: $CYPRESS_EXIT_CODE ==="
          echo "Memory after tests:"
          free -h

          # Check for Meteor crashes during tests
          if [ -f meteor-startup.log ]; then
            if grep -q "Error\|FATAL\|crashed" meteor-startup.log; then
              echo "=== Meteor errors detected during tests ==="
              grep -E "Error|FATAL|crashed" meteor-startup.log | tail -20
            fi
          fi

          exit $CYPRESS_EXIT_CODE
        env:
          CYPRESS_baseUrl: http://localhost:3000
          # Increase Cypress timeouts for slower environment
          CYPRESS_defaultCommandTimeout: 10000
          CYPRESS_requestTimeout: 10000
          CYPRESS_responseTimeout: 10000

      - name: Merge historical and current timing data
        if: always() && steps.specs.outputs.specs != ''
        working-directory: app
        run: |
          echo "=== Merging historical timing data with current test results ==="

          # Install xmlstarlet for XML manipulation
          sudo apt-get update -qq
          sudo apt-get install -y xmlstarlet

          # Create the final merged XML report for this shard
          CURRENT_REPORT="junit-xml-reports/junit-xml-report-${{ matrix.shard }}.xml"
          MERGED_REPORT="junit-xml-reports/junit-xml-report-${{ matrix.shard }}-merged.xml"

          if [ -f "$CURRENT_REPORT" ]; then
            echo "✓ Found current test report: $CURRENT_REPORT"

            # Start with current report as base
            cp "$CURRENT_REPORT" "$MERGED_REPORT"

            # If we have historical data, merge it
            if [ -d "junit-xml-reports-historical" ] && [ "$(find junit-xml-reports-historical -name '*.xml' | wc -l)" -gt 0 ]; then
              echo "✓ Found historical timing data, merging..."

              # Create a comprehensive merged report
              cat > merge_timing_data.py << 'EOF'
          import xml.etree.ElementTree as ET
          import sys
          import os
          import glob

          def merge_junit_reports(current_file, historical_dir, output_file):
              # Parse current report
              current_tree = ET.parse(current_file)
              current_root = current_tree.getroot()

              # Create a dictionary to store all test cases by their unique identifier
              all_testcases = {}

              # Add current test cases
              for testcase in current_root.findall('.//testcase'):
                  key = f"{testcase.get('classname', '')}.{testcase.get('name', '')}"
                  all_testcases[key] = testcase

              # Process historical reports
              for historical_file in glob.glob(os.path.join(historical_dir, '*.xml')):
                  try:
                      historical_tree = ET.parse(historical_file)
                      historical_root = historical_tree.getroot()

                      for testcase in historical_root.findall('.//testcase'):
                          key = f"{testcase.get('classname', '')}.{testcase.get('name', '')}"
                          # Only add if we don't already have this test from current run
                          if key not in all_testcases:
                              all_testcases[key] = testcase
                  except Exception as e:
                      print(f"Warning: Could not parse {historical_file}: {e}")

              # Create new merged report
              merged_root = ET.Element('testsuites')
              merged_suite = ET.SubElement(merged_root, 'testsuite')
              merged_suite.set('name', f'Merged E2E Tests - Shard ${{ matrix.shard }}')
              merged_suite.set('tests', str(len(all_testcases)))

              # Add all test cases to merged report
              for testcase in all_testcases.values():
                  merged_suite.append(testcase)

              # Write merged report
              merged_tree = ET.ElementTree(merged_root)
              merged_tree.write(output_file, encoding='utf-8', xml_declaration=True)

              print(f"✓ Merged {len(all_testcases)} test cases into {output_file}")

          if __name__ == "__main__":
              merge_junit_reports(sys.argv[1], sys.argv[2], sys.argv[3])
          EOF

              python3 merge_timing_data.py "$CURRENT_REPORT" "junit-xml-reports-historical" "$MERGED_REPORT"

              # Replace the original report with merged version
              mv "$MERGED_REPORT" "$CURRENT_REPORT"

            else
              echo "ℹ No historical timing data found, using current report only"
            fi

            echo "✓ Final report ready: $CURRENT_REPORT"
            echo "  Test cases in final report: $(xmlstarlet sel -t -v 'count(//testcase)' "$CURRENT_REPORT" 2>/dev/null || echo 'unknown')"

          else
            echo "⚠ No current test report found at $CURRENT_REPORT"
          fi

      - name: Upload test artifacts
        if: always() && steps.specs.outputs.specs != ''
        uses: actions/upload-artifact@v4
        with:
          name: cypress-results-shard-${{ matrix.shard }}
          path: |
            app/cypress/screenshots/
            app/cypress/videos/

      - name: Upload accumulated junit xml reports
        if: always() && steps.specs.outputs.specs != ''
        uses: actions/upload-artifact@v4
        with:
          name: junit-xml-reports-shard-${{ matrix.shard }}
          path: app/junit-xml-reports

  # Build and deploy only after all tests pass (on push to dev)
  build-and-deploy:
    # Continue even if e2e-tests are skipped
    if: |
      always() && 
      github.event_name == 'push' && 
      github.ref == 'refs/heads/dev' &&
      needs.unit-tests.result == 'success' &&
      (needs.e2e-tests.result == 'success' || needs.e2e-tests.result == 'skipped')
    needs: [unit-tests, e2e-tests]
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Login to OVH Harbor Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.OVHCLOUD_REGISTRY_URL }}
          username: ${{ vars.OVHCLOUD_REGISTRY_USERNAME }}
          password: ${{ secrets.OVHCLOUD_REGISTRY_ACCESS_TOKEN }}

      - name: Kubernetes set context
        uses: Azure/k8s-set-context@v4
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.OVHCLOUD_TEST_KUBE_CONFIG }}

      - name: Docker Build and Push, Kubernetes apply
        run: |
          # Get the current version from the version.js file
          CURRENT_VERSION=$(grep -o '"[0-9]\+\.[0-9]\+\.[0-9]\+"' app/imports/startup/client/version.js | tr -d '"')
          echo "Current version: $CURRENT_VERSION"

          # Use git commit short hash as build number for uniqueness
          BUILD_NUMBER=$(git rev-parse --short HEAD)
          echo "Build number: $BUILD_NUMBER"

          # Create release tag with build number for dev environment
          export RELEASE="${CURRENT_VERSION}-${BUILD_NUMBER}-dev"
          echo "Release tag: $RELEASE"

          # split the pieces
          REGISTRY_REPO="${{ vars.OVHCLOUD_REGISTRY_URL }}/${{ vars.OVHCLOUD_REGISTRY_PROJECT_NAME }}/springmath-dev"

          docker build -t "$REGISTRY_REPO:$RELEASE" .
          docker push  "$REGISTRY_REPO:$RELEASE"

          # correct way to add 'latest'
          docker tag  "$REGISTRY_REPO:$RELEASE" "$REGISTRY_REPO:latest"
          docker push "$REGISTRY_REPO:latest"

          # set up sm secrets - encode plain text vars to base64
          echo 'Setting up Kubernetes secrets...'
          DEV_ROOT_URL_B64=$(echo -n "${{ vars.DEV_ROOT_URL }}" | base64 -w 0)
          DEV_PORT_B64=$(echo -n "${{ vars.METEOR_PORT }}" | base64 -w 0)

          sed -i'' -e "s|DEV_ROOT_URL_SECRET|${DEV_ROOT_URL_B64}|g" \
            -e "s|DEV_MONGO_URL_SECRET|${{ secrets.DEV_MONGO_URL }}|g" \
            -e "s|DEV_MONGO_OPLOG_URL_SECRET|${{ secrets.DEV_MONGO_OPLOG_URL }}|g" \
            -e "s|DEV_PORT_SECRET|${DEV_PORT_B64}|g" \
            -e "s|DEV_METEOR_SETTINGS_SECRET|${{ secrets.DEV_METEOR_SETTINGS }}|g" \
            ./k8/dev.yml

          # set up docker creds
          export DOCKER_CONFIG=$(cat ~/.docker/config.json | base64 -w 0)
          sed -i'' -e "s|IMAGE_FULL_TAG|$REGISTRY_REPO:$RELEASE|g" \
            -e "s|DOCKER_CONFIG|$DOCKER_CONFIG|g" \
            ./k8/dev.yml

          echo 'applying yml file'

          # apply test
          kubectl apply -f ./k8/dev.yml

          # Wait for deployment to complete
          kubectl rollout status deployment/springmath-dev --timeout=600s

          # Deploy appropriate ingress based on SSL provider
          export DEV_SSL_PROVIDER="${{ vars.DEV_SSL_PROVIDER || 'cloudflare' }}"
          echo "Deploying ingress with SSL provider: $DEV_SSL_PROVIDER"
          ./k8/nginx-ingress-controller/deploy-ingress.sh dev $DEV_SSL_PROVIDER

      - name: Purge Cloudflare cache
        uses: jakejarvis/cloudflare-purge-action@master
        env:
          CLOUDFLARE_ZONE: ${{ vars.SPRINGMATH_CLOUDFLARE_ZONE_ID }}
          CLOUDFLARE_TOKEN: ${{ secrets.SPRINGMATH_CLOUDFLARE_PURGE_TOKEN }}
          PURGE_URLS: '["https://app.dev.springmath.com/*"]'
